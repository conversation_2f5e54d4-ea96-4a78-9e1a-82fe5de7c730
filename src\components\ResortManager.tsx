
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Building2,
  MapPin,
  Palette,
  Image,
  QrCode,
  Edit,
  Trash2,
  Plus,
  Upload,
  Eye,
  Users,
  Share2
} from "lucide-react";
import { toast } from "sonner";

const ResortManager = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingResort, setEditingResort] = useState(null);

  const resorts = [
    {
      id: 1,
      name: "Cabo Dreams Resort",
      location: "Los Cabos, Mexico",
      theme: "#0ea5e9",
      slug: "cabo-dreams",
      agents: 12,
      referrals: 1247,
      status: "Active",
      logo: "/placeholder.svg",
      heroImage: "/placeholder.svg"
    },
    {
      id: 2,
      name: "Riviera Paradise",
      location: "Riviera Maya, Mexico",
      theme: "#10b981",
      slug: "riviera-paradise",
      agents: 8,
      referrals: 892,
      status: "Active",
      logo: "/placeholder.svg",
      heroImage: "/placeholder.svg"
    },
    {
      id: 3,
      name: "Cancun Luxury Suites",
      location: "Cancún, Mexico",
      theme: "#8b5cf6",
      slug: "cancun-luxury",
      agents: 15,
      referrals: 1456,
      status: "Active",
      logo: "/placeholder.svg",
      heroImage: "/placeholder.svg"
    },
    {
      id: 4,
      name: "Playa Bonita Resort",
      location: "Playa del Carmen, Mexico",
      theme: "#f59e0b",
      slug: "playa-bonita",
      agents: 6,
      referrals: 634,
      status: "Inactive",
      logo: "/placeholder.svg",
      heroImage: "/placeholder.svg"
    }
  ];

  const handleAddResort = () => {
    setEditingResort(null);
    setIsDialogOpen(true);
  };

  const handleEditResort = (resort) => {
    setEditingResort(resort);
    setIsDialogOpen(true);
  };

  const handleSaveResort = () => {
    toast.success(editingResort ? "Resort updated successfully!" : "Resort created successfully!");
    setIsDialogOpen(false);
    setEditingResort(null);
  };

  const generateQRCode = (resort) => {
    toast.success(`QR code generated for ${resort.name}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-4xl font-bold">Resort Management</h2>
          <p className="text-slate-600 mt-1">Manage resort branding, settings, and configurations</p>
        </div>
        <Button onClick={handleAddResort} className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600">
          <Plus className="w-4 h-4 mr-2" />
          Add Resort
        </Button>
      </div>

      {/* Resort Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {resorts.map((resort,index) => (
          <Card key={resort.id} className="bg-white/70 backdrop-blur-sm border-slate-200 hover:shadow-xl transition-all duration-300 group">
            <div 
              className="h-32 rounded-t-lg  relative overflow-hidden"
              style={{ backgroundImage: `url('images/vida-res${index===1?"1":""}.jpg')` }}
            >
              <div className="absolute inset-0 bg-black/20"></div>
              <div className="absolute top-4 left-4 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                <Building2 className="w-6 h-6 text-white" />
              </div>
              <div className="absolute top-4 right-4">
                <Badge variant={resort.status === 'Active' ? 'default' : 'secondary'} className="bg-white/20 backdrop-blur-sm border-white/30">
                  {resort.status}
                </Badge>
              </div>
            </div>
            
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-xl font-bold text-slate-800">{resort.name}</h3>
                  <p className="text-slate-600 flex items-center mt-1">
                    <MapPin className="w-4 h-4 mr-1" />
                    {resort.location}
                  </p>
                </div>

                <div className="flex justify-between text-sm">
                  <div className="flex items-center">
                    <Users className="w-4 h-4 mr-1 text-slate-500" />
                    <span className="text-slate-600">{resort.agents} agents</span>
                  </div>
                  <div className="flex items-center">
                    <Share2 className="w-4 h-4 mr-1 text-slate-500" />
                    <span className="text-slate-600">{resort.referrals} referrals</span>
                  </div>
                </div>

                {/* <div className="flex items-center space-x-2">
                  <div 
                    className="w-4 h-4 rounded-full border-2 border-white shadow-sm"
                    style={{ backgroundColor: resort.theme }}
                  ></div>
                  <span className="text-sm text-slate-600">Theme: {resort.theme}</span>
                </div> */}

                <div className="flex space-x-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditResort(resort)}
                    className="flex-1"
                  >
                    <Edit className="w-4 h-4 mr-1" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => generateQRCode(resort)}
                    className="flex-1"
                  >
                    <QrCode className="w-4 h-4 mr-1" />
                    QR Code
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add/Edit Resort Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingResort ? 'Edit Resort' : 'Add New Resort'}
            </DialogTitle>
            <DialogDescription>
              {editingResort ? 'Update resort information and branding' : 'Create a new resort with branding and configuration'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Resort Name</Label>
                <Input 
                  id="name" 
                  placeholder="Resort name"
                  defaultValue={editingResort?.name}
                />
              </div>
              <div>
                <Label htmlFor="location">Location</Label>
                <Input 
                  id="location" 
                  placeholder="City, Country"
                  defaultValue={editingResort?.location}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="slug">URL Slug</Label>
                <Input 
                  id="slug" 
                  placeholder="resort-name"
                  defaultValue={editingResort?.slug}
                />
              </div>
              <div>
                <Label htmlFor="theme">Theme Color</Label>
                <div className="flex space-x-2">
                  <Input 
                    id="theme" 
                    type="color"
                    defaultValue={editingResort?.theme || "#0ea5e9"}
                    className="w-16 h-10 p-1 border rounded"
                  />
                  <Input 
                    placeholder="#0ea5e9"
                    defaultValue={editingResort?.theme}
                    className="flex-1"
                  />
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="welcome">Welcome Message</Label>
              <Textarea 
                id="welcome" 
                placeholder="Welcome message for guests"
                defaultValue={editingResort ? "Welcome to our beautiful resort! Share your experience with friends and family." : ""}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Resort Logo</Label>
                <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-slate-400 transition-colors cursor-pointer">
                  <Upload className="w-8 h-8 text-slate-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-600">Upload logo image</p>
                </div>
              </div>
              <div>
                <Label>Hero Image</Label>
                <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-slate-400 transition-colors cursor-pointer">
                  <Image className="w-8 h-8 text-slate-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-600">Upload hero image</p>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-6 border-t">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveResort} className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600">
                {editingResort ? 'Update Resort' : 'Create Resort'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ResortManager;
