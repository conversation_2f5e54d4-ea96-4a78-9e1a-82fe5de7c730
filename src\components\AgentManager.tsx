
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Users,
  Plus,
  Edit,
  QrCode,
  Copy,
  Building2,
  Mail,
  Phone,
  Calendar,
  TrendingUp,
  Search,
  Filter,
  Download,
  UserCheck,
  UserX,
  Eye,
  ArrowLeft
} from "lucide-react";
import { toast } from "sonner";
import AgentDetails from "./AgentDetails";

const AgentManager = () => {
  const [view, setView] = useState<"list" | "add" | "edit" | "details">("list");
  const [editingAgent, setEditingAgent] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAgentId, setSelectedAgentId] = useState("");

  const agents = [
    {
      id: "vikas123",
      name: "Vikas Patel",
      email: "<EMAIL>",
      phone: "******-0123",
      resort: "Cabo Dreams Resort",
      referrals: 156,
      completions: 68,
      joinDate: "2024-01-15",
      status: "Active",
      qrGenerated: true
    },
    {
      id: "maria456",
      name: "Maria Rodriguez",
      email: "<EMAIL>", 
      phone: "******-0456",
      resort: "Riviera Paradise",
      referrals: 134,
      completions: 61,
      joinDate: "2024-02-01",
      status: "Active",
      qrGenerated: true
    },
    {
      id: "carlos789",
      name: "Carlos Santos",
      email: "<EMAIL>",
      phone: "******-0789",
      resort: "Cancun Luxury Suites",
      referrals: 128,
      completions: 58,
      joinDate: "2024-01-20",
      status: "Active",
      qrGenerated: true
    },
    {
      id: "ana101",
      name: "Ana Martinez",
      email: "<EMAIL>",
      phone: "******-0101",
      resort: "Playa Bonita Resort",
      referrals: 45,
      completions: 22,
      joinDate: "2024-03-10",
      status: "Inactive",
      qrGenerated: false
    }
  ];

  const resorts = [
    "Cabo Dreams Resort",
    "Riviera Paradise", 
    "Cancun Luxury Suites",
    "Playa Bonita Resort"
  ];

  const filteredAgents = agents.filter(agent =>
    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.resort.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddAgent = () => {
    setEditingAgent(null);
    setView("add");
  };

  const handleEditAgent = (agent) => {
    setEditingAgent(agent);
    setView("edit");
  };

  const handleViewAgent = (agentId) => {
    setSelectedAgentId(agentId);
    setView("details");
  };

  const handleSaveAgent = () => {
    toast.success(editingAgent ? "Sales Sales Representative updated successfully!" : "Sales Sales Representative created successfully!");
    setView("list");
    setEditingAgent(null);
  };

  const copyQRCode = (agent) => {
    const qrUrl = `https://vida.app/download?rep=${agent.id}&resort=${agent.resort.toLowerCase().replace(/\s+/g, '-')}`;
    navigator.clipboard.writeText(qrUrl);
    toast.success("QR code URL copied to clipboard!");
  };

  const generateQRCode = (agent) => {
    toast.success(`QR code generated for ${agent.name}`);
  };

  if (view === "details") {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center space-x-4 mb-6">
          <Button variant="ghost" onClick={() => setView("list")} className="p-2">
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Sales Sales Representative Details
            </h2>
            <p className="text-muted-foreground mt-1">Comprehensive agent performance view</p>
          </div>
        </div>
        <AgentDetails 
          agentId={selectedAgentId}
          isOpen={true}
          onClose={() => setView("list")}
        />
      </div>
    );
  }

  if (view === "add" || view === "edit") {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center space-x-4 mb-6">
          <Button variant="ghost" onClick={() => setView("list")} className="p-2">
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              {view === "add" ? "Add New Agent" : "Edit Agent"}
            </h2>
            <p className="text-muted-foreground mt-1">
              {view === "add" ? "Create a new referral agent account" : "Update agent information and settings"}
            </p>
          </div>
        </div>

        <Card className="bg-white/80 backdrop-blur-sm border-border/50 shadow-lg max-w-4xl">
          <CardContent className="p-8">
            <div className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="agentName" className="text-base font-medium">Full Name</Label>
                  <Input 
                    id="agentName" 
                    placeholder="Sales Sales Representative full name"
                    defaultValue={editingAgent?.name}
                    className="mt-2 h-12"
                  />
                </div>
                <div>
                  <Label htmlFor="agentEmail" className="text-base font-medium">Email Address</Label>
                  <Input 
                    id="agentEmail" 
                    type="email"
                    placeholder="<EMAIL>"
                    defaultValue={editingAgent?.email}
                    className="mt-2 h-12"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="agentPhone" className="text-base font-medium">Phone Number</Label>
                  <Input 
                    id="agentPhone" 
                    placeholder="******-0123"
                    defaultValue={editingAgent?.phone}
                    className="mt-2 h-12"
                  />
                </div>
                <div>
                  <Label htmlFor="agentResort" className="text-base font-medium">Assigned Resort</Label>
                  <Select defaultValue={editingAgent?.resort}>
                    <SelectTrigger className="mt-2 h-12">
                      <SelectValue placeholder="Select resort" />
                    </SelectTrigger>
                    <SelectContent>
                      {resorts.map((resort) => (
                        <SelectItem key={resort} value={resort}>
                          {resort}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="agentId" className="text-base font-medium">Sales Sales Representative ID</Label>
                <Input 
                  id="agentId" 
                  placeholder="Auto-generated unique ID"
                  defaultValue={editingAgent?.id || `agent${Date.now()}`}
                  readOnly
                  className="mt-2 h-12 bg-muted/50"
                />
                <p className="text-sm text-muted-foreground mt-2">This ID will be used in QR codes and referral tracking</p>
              </div>

              <div className="flex justify-end space-x-4 pt-6 border-t">
                <Button variant="outline" onClick={() => setView("list")} className="px-8">
                  Cancel
                </Button>
                <Button onClick={handleSaveAgent} className="bg-gradient-to-r from-primary to-secondary px-8">
                  {view === "add" ? 'Create Agent' : 'Update Agent'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-4xl font-bold ">
           Sales Team
          </h2>
          <p className="text-muted-foreground mt-2 text-lg">Manage referral agents and their performance</p>
        </div>
        <Button onClick={handleAddAgent} className="bg-gradient-to-r from-primary to-secondary shadow-lg px-6">
          <Plus className="w-4 h-4 mr-2" />
          Add Sales Reps
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white/80 backdrop-blur-sm border-border/50 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground font-medium">Total</p>
                <p className="text-3xl font-bold text-foreground">{agents.length}</p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-500">
                <Users className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/80 backdrop-blur-sm border-border/50 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground font-medium">Active</p>
                <p className="text-3xl font-bold text-foreground">{agents.filter(a => a.status === 'Active').length}</p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-emerald-500">
                <UserCheck className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm border-border/50 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground font-medium">Avg Referrals</p>
                <p className="text-3xl font-bold text-foreground">
                  {Math.round(agents.reduce((sum, agent) => sum + agent.referrals, 0) / agents.length)}
                </p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm border-border/50 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground font-medium">Avg Lead Conversion</p>
                <p className="text-3xl font-bold text-foreground">
                  {Math.round(agents.reduce((sum, agent) => sum + agent.completions, 0) / agents.length)}%
                </p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500 to-red-500">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="bg-white/80 backdrop-blur-sm border-border/50 shadow-lg">
        <CardContent className="p-6">
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                <Input
                  placeholder="Search agents by name, email, or resort..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 h-12 text-base"
                />
              </div>
            </div>
            <Button variant="outline" className="px-6">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" className="px-6">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Sales Representative Table */}
      <Card className="bg-white/80 backdrop-blur-sm border-border/50 shadow-lg">
        <CardHeader>
          <CardTitle className="text-xl">Sales Representative Directory</CardTitle>
          <CardDescription className="text-base">Manage all referral sales reps and their details</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-base font-semibold">Sales Reps</TableHead>
                <TableHead className="text-base font-semibold">Resort</TableHead>
                <TableHead className="text-base font-semibold">Referrals</TableHead>
                <TableHead className="text-base font-semibold">Completion Rate</TableHead>
                <TableHead className="text-base font-semibold">Status</TableHead>
                <TableHead className="text-base font-semibold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAgents.map((agent) => (
                <TableRow key={agent.id} className="hover:bg-muted/30">
                  <TableCell>
                    <div>
                      <div className="font-semibold text-foreground text-base">{agent.name}</div>
                      <div className="text-sm text-muted-foreground flex items-center mt-1">
                        <Mail className="w-3 h-3 mr-1" />
                        {agent.email}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        ID: {agent.id}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Building2 className="w-4 h-4 mr-2 text-muted-foreground" />
                      <span className="font-medium">{agent.resort}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-semibold text-lg">{agent.referrals}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-semibold text-lg text-primary">{agent.completions}%</div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={agent.status === 'Active' ? 'default' : 'secondary'} className="font-medium">
                      {agent.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewAgent(agent.id)}
                        className="hover:bg-primary hover:text-white"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditAgent(agent)}
                        className="hover:bg-secondary hover:text-white"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyQRCode(agent)}
                        className="hover:bg-primary hover:text-white"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => generateQRCode(agent)}
                        className="hover:bg-secondary hover:text-white"
                      >
                        <QrCode className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default AgentManager;
